package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.controller;

import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.ReqInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.Result;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.PoiMatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

@RestController
@RequestMapping("/api/match")
public class PoiMatchController {
    private static final Logger logger = LoggerFactory.getLogger(PoiMatchController.class);

    @Autowired
    private PoiMatchService poiMatchService;

    /**
     * 测试入口：调用 matchPoi
     * POST /api/match/poi
     */
    @PostMapping("/poi")
    public Result matchPoi(@RequestBody ReqInfo reqInfo) {
        try {
            logger.info("matchPoi request: reqId={}, type={}, poiSize={}",
                    reqInfo != null ? reqInfo.getReqId() : null,
                    reqInfo != null ? reqInfo.getType() : null,
                    (reqInfo != null && reqInfo.getPoiInfos() != null) ? reqInfo.getPoiInfos().size() : null);
            Result result = poiMatchService.matchPoi(reqInfo);
            logger.info("matchPoi response: reqId={}, success={}, message={}",
                    result != null ? result.getReqId() : null,
                    result != null ? result.getSuccess() : null,
                    result != null ? result.getMessage() : null);
            return result;
        } catch (Exception e) {
            logger.error("matchPoi error", e);
            Result err = new Result();
            err.setReqId(reqInfo != null && reqInfo.getReqId() != null ? reqInfo.getReqId() : 0L);
            err.setSuccess(false);
            err.setMessage("internal error: " + e.getMessage());
            err.setMatchRecords(Collections.emptyList());
            return err;
        }
    }
}

