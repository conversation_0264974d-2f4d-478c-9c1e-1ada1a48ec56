package com.sankuai.algoplatform.waimaimatch.waimai.match.starter.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.ReqInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("listener")
public class MatchRequestConsumer {
    private static final Logger logger = LoggerFactory.getLogger(MatchRequestConsumer.class);

    @Autowired
    private ObjectMapper objectMapper;

    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(String msgBody) {
        logger.info("接收到消息：{}", msgBody);

        // 将 msgBody 反序列化为 ReqInfo 对象
        ReqInfo reqInfo = deserializeMsgBodyToReqInfo(msgBody);
        if (reqInfo != null) {
            logger.info("成功反序列化 ReqInfo: reqId={}, type={}, poiInfos数量={}",
                       reqInfo.getReqId(), reqInfo.getType(),
                       reqInfo.getPoiInfos() != null ? reqInfo.getPoiInfos().size() : 0);
            // 在这里处理反序列化后的 ReqInfo 对象

        } else {
            logger.warn("反序列化失败，无法处理消息");
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 将 msgBody（JSON 字符串）反序列化为 ReqInfo 对象
     *
     * @param msgBody JSON 字符串格式的消息体
     * @return ReqInfo 对象，反序列化失败时返回 null
     */
    public ReqInfo deserializeMsgBodyToReqInfo(String msgBody) {
        if (msgBody == null || msgBody.trim().isEmpty()) {
            logger.warn("msgBody 为空或空字符串");
            return null;
        }

        try {
            return objectMapper.readValue(msgBody, ReqInfo.class);
        } catch (JsonProcessingException e) {
            logger.error("JSON 反序列化失败，msgBody: {}", msgBody, e);
            return null;
        }
    }
}
