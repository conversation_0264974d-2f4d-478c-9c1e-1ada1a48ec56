package com.sankuai.algoplatform.waimaimatch.waimai.match.api.request;

import java.io.Serializable;

/**
 * 门店信息类
 */
public class PoiInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 临时门店序号，解析侧关联回传数据用
     */
    private Integer tempPoiId;

    /**
     * eid
     */
    private String eid;

    /**
     * storeId
     */
    private String storeId;

    /**
     * 扫点id
     */
    private String sdId;

    /**
     * 扫点纬度
     */
    private String sdLat;

    /**
     * 扫点经度
     */
    private String sdLon;

    /**
     * 商家纬度
     */
    private String poiLat;

    /**
     * 商家经度
     */
    private String poiLon;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店地址
     */
    private String poiAddress;

    /**
     * 二级物理城市id
     */
    private String secondLocationId;

    /**
     * 二级物理城市名称
     */
    private String secondLocationName;

    /**
     * 三级物理城市id
     */
    private String thridLocationId;

    /**
     * 三级物理城市名称
     */
    private String thridLocationName;

    /**
     * 月售
     */
    private String saleCnt;

    /**
     * 商家评分
     */
    private String rating;

    /**
     * 营业时间（B+无此字段）
     */
    private String openingHours;

    /**
     * 商家推荐理由
     */
    private String recommendReasonsStore;

    /**
     * 满减
     */
    private String manjianList;

    /**
     * 配送类型
     */
    private String deliveryType;

    /**
     * 配送距离
     */
    private String distance;

    /**
     * 起送价
     */
    private String startCharge;

    /**
     * 实付配送费
     */
    private String actualShipfee;

    /**
     * 原价配送费
     */
    private String noSubsidyFee;

    /**
     * 减配金额
     */
    private String subsidyFee;

    /**
     * 开店时间
     */
    private String firstOpenTime;

    // Getter and Setter methods

    public Integer getTempPoiId() {
        return tempPoiId;
    }

    public void setTempPoiId(Integer tempPoiId) {
        this.tempPoiId = tempPoiId;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getSdId() {
        return sdId;
    }

    public void setSdId(String sdId) {
        this.sdId = sdId;
    }

    public String getSdLat() {
        return sdLat;
    }

    public void setSdLat(String sdLat) {
        this.sdLat = sdLat;
    }

    public String getSdLon() {
        return sdLon;
    }

    public void setSdLon(String sdLon) {
        this.sdLon = sdLon;
    }

    public String getPoiLat() {
        return poiLat;
    }

    public void setPoiLat(String poiLat) {
        this.poiLat = poiLat;
    }

    public String getPoiLon() {
        return poiLon;
    }

    public void setPoiLon(String poiLon) {
        this.poiLon = poiLon;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public String getPoiAddress() {
        return poiAddress;
    }

    public void setPoiAddress(String poiAddress) {
        this.poiAddress = poiAddress;
    }

    public String getSecondLocationId() {
        return secondLocationId;
    }

    public void setSecondLocationId(String secondLocationId) {
        this.secondLocationId = secondLocationId;
    }

    public String getSecondLocationName() {
        return secondLocationName;
    }

    public void setSecondLocationName(String secondLocationName) {
        this.secondLocationName = secondLocationName;
    }

    public String getThridLocationId() {
        return thridLocationId;
    }

    public void setThridLocationId(String thridLocationId) {
        this.thridLocationId = thridLocationId;
    }

    public String getThridLocationName() {
        return thridLocationName;
    }

    public void setThridLocationName(String thridLocationName) {
        this.thridLocationName = thridLocationName;
    }

    public String getSaleCnt() {
        return saleCnt;
    }

    public void setSaleCnt(String saleCnt) {
        this.saleCnt = saleCnt;
    }

    public String getRating() {
        return rating;
    }

    public void setRating(String rating) {
        this.rating = rating;
    }

    public String getOpeningHours() {
        return openingHours;
    }

    public void setOpeningHours(String openingHours) {
        this.openingHours = openingHours;
    }

    public String getRecommendReasonsStore() {
        return recommendReasonsStore;
    }

    public void setRecommendReasonsStore(String recommendReasonsStore) {
        this.recommendReasonsStore = recommendReasonsStore;
    }

    public String getManjianList() {
        return manjianList;
    }

    public void setManjianList(String manjianList) {
        this.manjianList = manjianList;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getStartCharge() {
        return startCharge;
    }

    public void setStartCharge(String startCharge) {
        this.startCharge = startCharge;
    }

    public String getActualShipfee() {
        return actualShipfee;
    }

    public void setActualShipfee(String actualShipfee) {
        this.actualShipfee = actualShipfee;
    }

    public String getNoSubsidyFee() {
        return noSubsidyFee;
    }

    public void setNoSubsidyFee(String noSubsidyFee) {
        this.noSubsidyFee = noSubsidyFee;
    }

    public String getSubsidyFee() {
        return subsidyFee;
    }

    public void setSubsidyFee(String subsidyFee) {
        this.subsidyFee = subsidyFee;
    }

    public String getFirstOpenTime() {
        return firstOpenTime;
    }

    public void setFirstOpenTime(String firstOpenTime) {
        this.firstOpenTime = firstOpenTime;
    }
}
