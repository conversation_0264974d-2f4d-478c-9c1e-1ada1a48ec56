package com.sankuai.algoplatform.waimaimatch.waimai.match.api.request;

import java.io.Serializable;
import java.util.List;

/**
 * 请求信息类
 */
public class ReqInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private Long reqId;

    /**
     * 数据来源，B+商家列表、B+拼团列表等
     */
    private String type;

    /**
     * 是否可以生成新ID
     */
    private Boolean canNewId;

    /**
     * 门店信息列表
     */
    private List<PoiInfo> poiInfos;

    public Long getReqId() {
        return reqId;
    }

    public void setReqId(Long reqId) {
        this.reqId = reqId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getCanNewId() {
        return canNewId;
    }

    public void setCanNewId(Boolean canNewId) {
        this.canNewId = canNewId;
    }

    public List<PoiInfo> getPoiInfos() {
        return poiInfos;
    }

    public void setPoiInfos(List<PoiInfo> poiInfos) {
        this.poiInfos = poiInfos;
    }

    // 构造函数

    /**
     * 默认构造函数
     */
    public ReqInfo() {
    }

    /**
     * 带参数的构造函数
     *
     * @param reqId 请求ID
     * @param type 请求类型
     * @param canNewId 是否可以生成新ID
     * @param poiInfos 门店信息列表
     */
    public ReqInfo(Long reqId, String type, Boolean canNewId, List<PoiInfo> poiInfos) {
        this.reqId = reqId;
        this.type = type;
        this.canNewId = canNewId;
        this.poiInfos = poiInfos;
    }
}
