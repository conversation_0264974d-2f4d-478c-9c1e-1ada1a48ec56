package com.sankuai.algoplatform.waimaimatch.waimai.match.api.response;

import java.io.Serializable;

public class Response <T> implements Serializable {
    private boolean success;
    private T data;
    private String message;

    private String traceId;

    public String getTraceId() {
        return traceId;
    }
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Response(boolean success) {
        this.success = success;
    }

    public Response(boolean success, T data) {
        this.success = success;
        this.data = data;
    }

    public Response(boolean success, T data, String message) {
        this.success = success;
        this.data = data;
        this.message = message;
    }

    public boolean ifSuccess() {
        return this.success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
