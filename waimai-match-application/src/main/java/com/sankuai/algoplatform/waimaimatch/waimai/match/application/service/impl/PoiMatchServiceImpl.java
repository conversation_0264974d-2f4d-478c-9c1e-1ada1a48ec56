package com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.ReqInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.MatchRecord;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.Result;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.PoiMatchService;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.tair.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.es.EsClient;
import com.sankuai.meituan.poros.client.PorosApiClient;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch._types.FieldValue;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PoiMatchServiceImpl implements PoiMatchService {
    @MdpThriftClient(remoteAppKey = "com.sankuai.algoplatform.predictor", remoteServerPort = 9001, timeout = 60000)
    private TPredictService.Iface predictService;

    @Autowired
    private TairClient tairClient;

    @Override
    public Result matchPoi(ReqInfo reqInfo) {
        // 基本健壮性校验
        if (reqInfo == null || reqInfo.getPoiInfos() == null || reqInfo.getPoiInfos().isEmpty()) {
            return buildEmptyResult(reqInfo);
        }

        List<PoiInfo> poiInfos = reqInfo.getPoiInfos();
        Map<Integer, MatchRecord> finalByTempId = new LinkedHashMap<>();
        Set<Integer> handledTempIds = new HashSet<>();

        // 1. Tair 缓存匹配
        step1TairCacheMatch(poiInfos, finalByTempId, handledTempIds);

        // 2. ES 精确匹配
        step2EsExactMatch(poiInfos, handledTempIds, finalByTempId);

        // 3. 坐标验证与过滤
        List<PoiInfo> toPredict = step3CoordinateValidation(poiInfos, handledTempIds, finalByTempId);

        // 4&5. 模型预测与缓存写入
        PredictResult predictResult = step4And5ModelPredictAndCache(toPredict, reqInfo, finalByTempId, handledTempIds);

        // 6. 新商户处理
        step6NewMerchantHandling(reqInfo, poiInfos, finalByTempId);

        // 汇总结果
        List<MatchRecord> records = buildFinalRecords(poiInfos, finalByTempId);

        return buildResult(reqInfo, predictResult, records);
    }

    // ========== 分步处理方法 ==========

    // 构建空结果
    private Result buildEmptyResult(ReqInfo reqInfo) {
        Result result = new Result();
        result.setReqId(reqInfo != null && reqInfo.getReqId() != null ? reqInfo.getReqId() : 0L);
        result.setSuccess(true);
        result.setMessage("empty poiInfos");
        result.setMatchRecords(Collections.emptyList());
        return result;
    }

    // 第1步：Tair 缓存匹配
    private void step1TairCacheMatch(List<PoiInfo> poiInfos, Map<Integer, MatchRecord> finalByTempId, Set<Integer> handledTempIds) {
        Map<String, PoiInfo> id2Poi = new HashMap<>();
        List<String> tairKeys = new ArrayList<>();

        for (PoiInfo p : poiInfos) {
            String id = firstNonBlank(p.getStoreId(), p.getEid());
            if (!isBlank(id)) {
                tairKeys.add(id);
                id2Poi.put(id, p);
            }
        }

        if (!tairKeys.isEmpty()) {
            Map<String, String> cached = safeBatchGet("match_result_", tairKeys);
            for (Map.Entry<String, String> e : cached.entrySet()) {
                PoiInfo poi = id2Poi.get(e.getKey());
                if (poi == null) continue;

                Integer tempId = poi.getTempPoiId();
                if (tempId == null) continue;

                String json = e.getValue();
                String matchId = extractMatchId(json);
                if (isBlank(matchId)) continue;

                MatchRecord rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchId(matchId);
                rec.setMatchType(2); // Tair 命中
                finalByTempId.put(tempId, rec);
                handledTempIds.add(tempId);
            }
        }
    }

    // 第2步：ES 精确匹配
    private void step2EsExactMatch(List<PoiInfo> poiInfos, Set<Integer> handledTempIds, Map<Integer, MatchRecord> finalByTempId) {
        List<PoiInfo> toEs = poiInfos.stream()
                .filter(p -> p.getTempPoiId() != null && !handledTempIds.contains(p.getTempPoiId()))
                .filter(p -> !isBlank(p.getStoreId()))
                .collect(Collectors.toList());

        if (!toEs.isEmpty()) {
            Map<String, String> esMatches = findExactMatchFromEs(toEs);
            for (PoiInfo p : toEs) {
                String poiId = esMatches.get(p.getStoreId());
                if (!isBlank(poiId)) {
                    Integer tempId = p.getTempPoiId();
                    MatchRecord rec = new MatchRecord();
                    rec.setTempPoiId(tempId);
                    rec.setMatchId(poiId);
                    rec.setMatchType(1); // ES 精确匹配
                    finalByTempId.put(tempId, rec);
                    handledTempIds.add(tempId);
                }
            }
        }
    }

    // 第3步：坐标验证与过滤
    private List<PoiInfo> step3CoordinateValidation(List<PoiInfo> poiInfos, Set<Integer> handledTempIds, Map<Integer, MatchRecord> finalByTempId) {
        List<PoiInfo> remainAfterEs = poiInfos.stream()
                .filter(p -> p.getTempPoiId() != null && !handledTempIds.contains(p.getTempPoiId()))
                .collect(Collectors.toList());

        List<PoiInfo> toPredict = new ArrayList<>();
        for (PoiInfo p : remainAfterEs) {
            boolean noCoords = allEmpty(p.getPoiLat(), p.getPoiLon()) && allEmpty(p.getSdLat(), p.getSdLon());
            if (noCoords) {
                Integer tempId = p.getTempPoiId();
                MatchRecord rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchType(-1); // 无坐标
                finalByTempId.put(tempId, rec);
                handledTempIds.add(tempId);
            } else {
                toPredict.add(p);
            }
        }
        return toPredict;
    }

    // 第4&5步：模型预测与缓存写入
    private PredictResult step4And5ModelPredictAndCache(List<PoiInfo> toPredict, ReqInfo reqInfo,
                                                        Map<Integer, MatchRecord> finalByTempId, Set<Integer> handledTempIds) {
        if (toPredict.isEmpty()) {
            return new PredictResult(true, "ok");
        }

        try {
            List<Map<String, Object>> poiFields = toPredict.stream()
                    .map(this::poiInfoToMap)
                    .collect(Collectors.toList());
            Map<String, String> extra = buildExtra(reqInfo);

            TPredictResponse resp = doPredictReflect("poi_match", poiFields, extra);
            int code = resp.getCode();
            boolean predictOk = code == 0;
            Map<String, String> data = resp.getData();

            if (predictOk) {
                String result = data.get("result");
                List<Map<String, Object>> resultList = JSON.parseObject(result, new TypeReference<List<Map<String, Object>>>() {});

                // 收集需要写入缓存的匹配结果
                Map<String, String> cacheEntries = new HashMap<>();

                // 将模型结果填充到 finalByTempId
                for (Map<String, Object> m : resultList) {
                    Integer tempId = asInt(m.get("tempPoiId"));
                    if (tempId == null || finalByTempId.containsKey(tempId)) continue;

                    String matchId = asStr(m.get("poi_id"));
                    Double score = asDouble(m.get("score"));

                    // 找到对应的 PoiInfo 以获取 storeId/eid 用于缓存
                    PoiInfo correspondingPoi = findPoiByTempId(toPredict, tempId);
                    if (correspondingPoi != null && !isBlank(matchId)) {
                        String cacheKey = firstNonBlank(correspondingPoi.getStoreId(), correspondingPoi.getEid());
                        if (!isBlank(cacheKey)) {
                            String cacheValue = buildCacheValue(matchId, score);
                            cacheEntries.put("match_result_" + cacheKey, cacheValue);
                        }
                    }

                    MatchRecord rec = new MatchRecord();
                    rec.setTempPoiId(tempId);
                    rec.setMatchId(matchId);
                    rec.setScore(score);
                    rec.setMatchType(MatchRecord.MATCH_TYPE_REALTIME);

                    finalByTempId.put(tempId, rec);
                    handledTempIds.add(tempId);
                }

                // 异步写入缓存
                if (!cacheEntries.isEmpty()) {
                    writeToCacheAsync(cacheEntries);
                }
            }

            return new PredictResult(predictOk, predictOk ? "ok" : "predict failed");

        } catch (Exception ex) {
            return new PredictResult(false, "predict error: " + ex.getMessage());
        }
    }

    // 第6步：新商户处理
    private void step6NewMerchantHandling(ReqInfo reqInfo, List<PoiInfo> poiInfos, Map<Integer, MatchRecord> finalByTempId) {
        if (reqInfo.getCanNewId() == null || !reqInfo.getCanNewId()) {
            return;
        }

        String today = getCurrentDateString();
        for (PoiInfo p : poiInfos) {
            Integer tempId = p.getTempPoiId();
            if (tempId == null || finalByTempId.containsKey(tempId)) continue;

            String matchPoiId = firstNonBlank(p.getStoreId(), p.getEid());
            if (!isBlank(matchPoiId)) {
                String newId = generateNewId(today, matchPoiId);

                MatchRecord rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setNewId(newId);
                rec.setMatchType(0); // 新商户，不匹配
                finalByTempId.put(tempId, rec);
            }
        }
    }

    // 汇总最终结果
    private List<MatchRecord> buildFinalRecords(List<PoiInfo> poiInfos, Map<Integer, MatchRecord> finalByTempId) {
        List<MatchRecord> records = new ArrayList<>(poiInfos.size());
        for (PoiInfo p : poiInfos) {
            Integer tempId = p.getTempPoiId();
            if (tempId == null) continue;

            MatchRecord rec = finalByTempId.get(tempId);
            if (rec == null) {
                rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchType(0); // 不匹配
            }
            records.add(rec);
        }
        return records;
    }

    // 构建最终结果
    private Result buildResult(ReqInfo reqInfo, PredictResult predictResult, List<MatchRecord> records) {
        Result out = new Result();
        Long finalReqId = (predictResult.reqIdOverride != null) ? predictResult.reqIdOverride : reqInfo.getReqId();
        out.setReqId(finalReqId == null ? 0L : finalReqId);
        out.setSuccess(predictResult.success);
        out.setMessage(predictResult.message);
        out.setMatchRecords(records);
        return out;
    }

    // 预测结果封装类
    private static class PredictResult {
        boolean success = true;
        String message = "ok";
        Long reqIdOverride = null;

        PredictResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        PredictResult(boolean success, String message, Long reqIdOverride) {
            this.success = success;
            this.message = message;
            this.reqIdOverride = reqIdOverride;
        }
    }

    // ========== 私有方法 ==========

    private Map<String, String> safeBatchGet(String prefix, Collection<String> keys) {
        try {
            return tairClient.batchGet(prefix, keys);
        } catch (Throwable t) {
            return Collections.emptyMap();
        }
    }

    // 解析 Tair 中的 JSON，尽力提取 matchId/poiId/poi_id
    private String extractMatchId(String json) {
        if (isBlank(json)) return null;
        // 尝试常见 key
        String[] keys = new String[]{"\"matchId\"", "\"poiId\"", "\"poi_id\""};
        for (String k : keys) {
            String v = extractJsonString(json, k);
            if (!isBlank(v)) return v;
        }
        return null;
    }

    private String extractJsonString(String json, String quotedKey) {
        try {
            int idx = json.indexOf(quotedKey);
            if (idx < 0) return null;
            int colon = json.indexOf(':', idx + quotedKey.length());
            if (colon < 0) return null;
            int startQuote = json.indexOf('"', colon + 1);
            if (startQuote < 0) return null;
            int endQuote = json.indexOf('"', startQuote + 1);
            if (endQuote < 0) return null;
            return json.substring(startQuote + 1, endQuote);
        } catch (Exception ignore) {
            return null;
        }
    }

    // ES 精确匹配：按 store_id terms 精确查询，返回 storeId -> poi_id
    private Map<String, String> findExactMatchFromEs(List<PoiInfo> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        List<String> storeIds = list.stream()
                .map(PoiInfo::getStoreId)
                .filter(s -> !isBlank(s))
                .distinct()
                .collect(Collectors.toList());
        if (storeIds.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            PorosApiClient client = EsClient.getEsClient();
            List<FieldValue> values = storeIds.stream().map(FieldValue::of).collect(Collectors.toList());
            SearchResponse<Map> response = client.search(s -> s
                            .index("waimai-bml-match")
                            .size(storeIds.size())
                            .query(q -> q
                                    .terms(t -> t
                                            .field("store_id")
                                            .terms(tt -> tt.value(values))
                                    )
                            )
                            .source(src -> src
                                    .filter(f -> f.includes(Arrays.asList("store_id", "poi_id")))
                            ),
                    Map.class);

            Map<String, String> res = new HashMap<>();
            if (response != null && response.hits() != null && response.hits().hits() != null) {
                for (Hit<Map> hit : response.hits().hits()) {
                    Map<String, Object> src = hit.source();
                    if (src == null) { continue; }
                    Object sid = src.get("store_id");
                    Object pid = src.get("poi_id");
                    if (sid != null && pid != null) {
                        res.put(String.valueOf(sid), String.valueOf(pid));
                    }
                }
            }
            return res;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private Map<String, Object> poiInfoToMap(PoiInfo p) {
        Map<String, Object> m = new HashMap<>();
        // 仅映射已定义字段，按需扩展
        m.put("tempPoiId", p.getTempPoiId());
        m.put("eid", p.getEid());
        m.put("storeId", p.getStoreId());
        m.put("sdId", p.getSdId());
        m.put("sdLat", p.getSdLat());
        m.put("sdLon", p.getSdLon());
        m.put("poiLat", p.getPoiLat());
        m.put("poiLon", p.getPoiLon());
        m.put("poiName", p.getPoiName());
        m.put("poiAddress", p.getPoiAddress());
        m.put("secondLocationId", p.getSecondLocationId());
        m.put("secondLocationName", p.getSecondLocationName());
        m.put("thridLocationId", p.getThridLocationId());
        m.put("thridLocationName", p.getThridLocationName());
        m.put("saleCnt", p.getSaleCnt());
        m.put("rating", p.getRating());
        m.put("openingHours", p.getOpeningHours());
        m.put("recommendReasonsStore", p.getRecommendReasonsStore());
        m.put("manjianList", p.getManjianList());
        m.put("deliveryType", p.getDeliveryType());
        m.put("distance", p.getDistance());
        m.put("startCharge", p.getStartCharge());
        m.put("actualShipfee", p.getActualShipfee());
        m.put("noSubsidyFee", p.getNoSubsidyFee());
        m.put("subsidyFee", p.getSubsidyFee());
        m.put("firstOpenTime", p.getFirstOpenTime());
        return m;
    }

    private Map<String, String> buildExtra(ReqInfo reqInfo) {
        Map<String, String> extra = new HashMap<>();
        extra.put("reqId", reqInfo.getReqId() != null ? reqInfo.getReqId().toString() : null);
        extra.put("type", reqInfo.getType());
        extra.put("canNewId", reqInfo.getCanNewId() != null ? reqInfo.getCanNewId().toString() : null);
        return extra;
    }

    // 通过反射构造并调用 TPredictService#predict
    private TPredictResponse doPredictReflect(String bizCode, List<Map<String, Object>> poiFields, Map<String, String> extra) throws Exception {
        TPredictRequest tPredictRequest = new TPredictRequest();
        tPredictRequest.setBizCode(bizCode);
        // data 封装 poiFields
        Map<String, String> data = new HashMap<>();
        data.put("poiFields", JSONObject.toJSONString(poiFields));
        tPredictRequest.setReq(data);
        // extra
        tPredictRequest.setExtra(extra);

        return predictService.predict(tPredictRequest);
    }

    private void invokeSetterIfExists(Class<?> clz, Object obj, String method, Class<?> paramType, Object value) {
        try {
            Method m = clz.getMethod(method, paramType);
            m.invoke(obj, value);
        } catch (Exception ignore) {
            // 允许请求结构差异，不抛错
        }
    }

    private Object invokeGetter(Class<?> clz, Object obj, String getter) {
        try {
            Method m = clz.getMethod(getter);
            return m.invoke(obj);
        } catch (Exception e) {
            return null;
        }
    }

    // 根据 tempId 查找对应的 PoiInfo
    private PoiInfo findPoiByTempId(List<PoiInfo> poiList, Integer tempId) {
        if (poiList == null || tempId == null) {
            return null;
        }
        return poiList.stream()
                .filter(p -> tempId.equals(p.getTempPoiId()))
                .findFirst()
                .orElse(null);
    }

    // 构造缓存值：MatchRecord 的 JSON 表示
    private String buildCacheValue(String matchId, Double score) {
        Map<String, Object> cacheData = new HashMap<>();
        cacheData.put("matchId", matchId);
        cacheData.put("poiId", matchId); // 兼容性，同时存储 poiId
        cacheData.put("poi_id", matchId); // 兼容性，同时存储 poi_id
        if (score != null) {
            cacheData.put("score", score);
        }
        cacheData.put("timestamp", System.currentTimeMillis());
        return JSONObject.toJSONString(cacheData);
    }

    // 异步写入缓存，不影响主流程
    private void writeToCacheAsync(Map<String, String> cacheEntries) {
        CompletableFuture.runAsync(() -> {
            try {
                // 缓存 24 小时过期
                Map<String, Boolean> results = tairClient.batchPut(cacheEntries, 24, TimeUnit.HOURS);
                long successCount = results.values().stream().mapToLong(success -> success ? 1 : 0).sum();
                // 这里可以添加日志记录缓存写入结果
                if (successCount < cacheEntries.size()) {
                    // 部分写入失败，可以记录日志但不影响主流程
                }
            } catch (Exception e) {
                // 缓存写入失败不影响主流程，只记录日志
                log.error("writeToCacheAsync error", e);
            }
        });
    }

    // 获取当前日期字符串，格式为 yyyyMMdd
    private String getCurrentDateString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }

    // 生成新商户 ID：md5(concat('W', substr(today,3,8), matchPoiId))
    private String generateNewId(String today, String matchPoiId) {
        try {
            // substr(today,3,8) 表示从第3位开始取8位，即去掉前2位年份
            String datePart = today.length() >= 8 ? today.substring(2, 8) : today;
            String input = "W" + datePart + matchPoiId;

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));

            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            // 容错处理，返回简单拼接
            return "W" + System.currentTimeMillis() + "_" + matchPoiId;
        }
    }

    private static boolean isBlank(String s) { return s == null || s.trim().isEmpty(); }
    private static boolean allEmpty(String a, String b) { return isBlank(a) && isBlank(b); }
    private static String firstNonBlank(String a, String b) { return !isBlank(a) ? a : b; }

    private static Object firstNonNull(Object a, Object b) { return a != null ? a : b; }
    private static String asStr(Object o) { return o == null ? null : String.valueOf(o); }
    private static Integer asInt(Object o) {
        if (o == null) return null; if (o instanceof Integer) return (Integer)o; try { return Integer.parseInt(String.valueOf(o)); } catch (Exception e) { return null; }
    }
    private static Double asDouble(Object o) {
        if (o == null) return null; if (o instanceof Double) return (Double)o; try { return Double.parseDouble(String.valueOf(o)); } catch (Exception e) { return null; }
    }
}
