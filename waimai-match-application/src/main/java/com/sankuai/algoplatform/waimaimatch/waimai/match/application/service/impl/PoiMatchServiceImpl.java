package com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.impl;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.ReqInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.Result;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.PoiMatchService;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PoiMatchServiceImpl implements PoiMatchService {
    @MdpThriftClient(remoteAppKey = "com.sankuai.algoplatform.predictor", remoteServerPort = 9001, timeout = 60000)
    private TPredictService.Iface predictService;

    @Override
    public Result matchPoi(ReqInfo reqInfo) {

        return null;
    }
}
