package com.sankuai.algoplatform.waimaimatch.waimai.match.application.es;

import com.sankuai.meituan.poros.client.PorosApiAsyncClient;
import com.sankuai.meituan.poros.client.PorosApiClient;
import com.sankuai.meituan.poros.client.PorosApiClientBuilder;
import com.sankuai.meituan.poros.client.PorosApiClients;

public class EsClient {
    public static PorosApiClients porosApiClients = PorosApiClientBuilder.builder()
            .clusterName("grocery_eaglenode-bmlmatch_default")
            .appKey("com.sankuai.algoplatform.waimaimatch")
            .timeoutMillis(10000) //全局超时时间。单位ms
            .buildClients();

    public static PorosApiClient getEsClient(){
        return porosApiClients.getPorosApiClient();
    }

    public static PorosApiAsyncClient getEsAsyncClient(){
        return porosApiClients.getPorosApiAsyncClient();
    }
}
