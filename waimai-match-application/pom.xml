<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform.waimaimatch</groupId>
        <artifactId>waimai-match</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>waimai-match-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>waimai-match-application</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.algoplatform.waimaimatch</groupId>
            <artifactId>waimai-match-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.algoplatform.waimaimatch</groupId>
            <artifactId>waimai-match-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-java-api-client</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-client</artifactId>
            <version>0.9.23</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>predictor-client</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>
</project>
