package com.sankuai.algoplatform.waimaimatch.waimai.match.infrastructure.producer;

import com.meituan.mafka.client.producer.IProducerProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class MatchResultProducer {
    @Autowired
    @Qualifier("resultProducer")
    private IProducerProcessor producer0;
}
